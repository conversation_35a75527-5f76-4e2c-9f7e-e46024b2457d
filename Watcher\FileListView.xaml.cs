using CommunityToolkit.Mvvm.DependencyInjection;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Controls.Primitives;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Navigation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Threading;
using Watcher.Services;
using Watcher.Services;

namespace Watcher
{
    public sealed partial class FileListView : UserControl
    {
        FileListViewModel ViewModel { get; set; }
        private CancellationTokenSource _conversionCancellationTokenSource;

        public FileListView()
        {
            this.InitializeComponent();
            this.ViewModel = Ioc.Default.GetRequiredService<FileListViewModel>();

            // 添加键盘事件处理
            this.Loaded += FileListView_Loaded;
        }

        private void FileListView_Loaded(object sender, RoutedEventArgs e)
        {
            // 找到ListBox并添加键盘事件处理
            var listBox = this.FindChild<ListBox>();
            if (listBox != null)
            {
                listBox.KeyDown += ListBox_KeyDown;
                listBox.IsTabStop = true;
            }
            
            // 订阅ViewModel的文件夹选择事件
            ViewModel.PropertyChanged += ViewModel_PropertyChanged;
        }

        private async void ViewModel_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // 当选择的文件夹发生变化时，执行视频转换
            if (e.PropertyName == nameof(FileListViewModel.SelectedFolder))
            {
                await ConvertVideosInFolderAsync(ViewModel.SelectedFolder);
            }
        }

        private void ListBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
            // 处理空格键，让父窗口处理播放/暂停
            if (e.Key == VirtualKey.Space)
            {
                // 不处理空格键，让事件冒泡到主窗口
                e.Handled = false;
                Debug.WriteLine("ListBox收到空格键，不处理，让主窗口处理");
                return;
            }
        }

        /// <summary>
        /// 转换指定文件夹中的所有视频文件
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        private async Task ConvertVideosInFolderAsync(string folderPath)
        {
            if (string.IsNullOrEmpty(folderPath) || !Directory.Exists(folderPath))
                return;

            // 获取所有.dav和.mp4文件（排除已经转换的文件）
            var videoFiles = Directory.GetFiles(folderPath)
                .Where(file => {
                    var ext = Path.GetExtension(file).ToLower();
                    var name = Path.GetFileNameWithoutExtension(file);
                    return (ext == ".dav" || ext == ".mp4") && !name.EndsWith("Z");
                })
                .ToList();

            if (videoFiles.Count == 0)
                return;

            // 显示进度条
            ConversionProgressBar.Visibility = Visibility.Visible;
            ConversionProgressBar.Value = 0;
            ConversionProgressBar.Maximum = videoFiles.Count;

            try
            {
                _conversionCancellationTokenSource = new CancellationTokenSource();
                var token = _conversionCancellationTokenSource.Token;

                for (int i = 0; i < videoFiles.Count; i++)
                {
                    // 检查取消请求
                    if (token.IsCancellationRequested)
                        break;

                    var inputFile = videoFiles[i];
                    var fileNameWithoutExt = Path.GetFileNameWithoutExtension(inputFile);
                    var outputFile = Path.Combine(folderPath, $"{fileNameWithoutExt}Z.mp4");

                    // 如果输出文件已存在，则跳过
                    if (File.Exists(outputFile))
                    {
                        ConversionProgressBar.Value = i + 1;
                        continue;
                    }

                    // 执行FFmpeg命令
                    await ConvertVideoAsync(inputFile, outputFile, token);

                    // 更新进度条
                    ConversionProgressBar.Value = i + 1;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"视频转换过程中发生错误: {ex.Message}");
            }
            finally
            {
                // 隐藏进度条
                ConversionProgressBar.Visibility = Visibility.Collapsed;
                _conversionCancellationTokenSource?.Dispose();
                _conversionCancellationTokenSource = null;
                
                // 刷新文件列表
                ViewModel.RefreshCommand.Execute(null);
            }
        }

        /// <summary>
        /// 使用FFmpeg转换单个视频文件
        /// </summary>
        /// <param name="inputFile">输入文件路径</param>
        /// <param name="outputFile">输出文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task ConvertVideoAsync(string inputFile, string outputFile, CancellationToken cancellationToken)
        {
            try
            {
                // 从配置服务获取FFmpeg路径
                var configService = Ioc.Default.GetRequiredService<ConfigurationService>();
                var ffmpegPath = configService.GetFFmpegPath();
                Debug.WriteLine("当前ffmpeg路径为:" + ffmpegPath);
                if (!File.Exists(ffmpegPath))
                {
                    Debug.WriteLine("FFmpeg未找到，请确保FFmpeg文件夹包含ffmpeg.exe");
                    return;
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = ffmpegPath,
                    Arguments = $"-v quiet -f h264 -r 25 -i \"{inputFile}\" -c:v copy -an \"{outputFile}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = new Process { StartInfo = startInfo };
                process.Start();
                
                // 等待进程完成或被取消
                while (!process.HasExited && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(100, cancellationToken);
                }

                if (cancellationToken.IsCancellationRequested && !process.HasExited)
                {
                    process.Kill();
                }

                await process.WaitForExitAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"转换视频文件时发生错误: {ex.Message}");
            }
        }

        // 辅助方法：查找子控件
        private T? FindChild<T>() where T : DependencyObject
        {
            return FindChild<T>(this);
        }

        private T? FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childResult = FindChild<T>(child);
                if (childResult != null)
                    return childResult;
            }
            return null;
        }
    }
}
